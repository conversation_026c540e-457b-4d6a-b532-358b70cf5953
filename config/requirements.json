{"python": {"min_version": "3.8.0"}, "node": {"min_version": "16.0.0", "required_for": ["mcp"]}, "disk_space_mb": 500, "external_tools": {"claude_cli": {"command": "claude --version", "min_version": "0.1.0", "required_for": ["mcp"], "optional": false}, "git": {"command": "git --version", "min_version": "2.0.0", "required_for": ["development"], "optional": true}}, "installation_commands": {"python": {"linux": "sudo apt update && sudo apt install python3 python3-pip", "darwin": "brew install python3", "win32": "Download Python from https://python.org/downloads/", "description": "Python 3.8+ is required for SuperClaude framework"}, "node": {"linux": "sudo apt update && sudo apt install nodejs npm", "darwin": "brew install node", "win32": "Download Node.js from https://nodejs.org/", "description": "Node.js 16+ is required for MCP server integration"}, "claude_cli": {"all": "Visit https://claude.ai/code for installation instructions", "description": "Claude CLI is required for MCP server management"}, "git": {"linux": "sudo apt update && sudo apt install git", "darwin": "brew install git", "win32": "Download Git from https://git-scm.com/downloads", "description": "Git is recommended for development workflows"}, "npm": {"linux": "sudo apt update && sudo apt install npm", "darwin": "npm is included with Node.js", "win32": "npm is included with Node.js", "description": "npm is required for installing MCP servers"}}}